<?xml version="1.0" encoding="UTF-8"?>
<svg width="200px" height="200px" viewBox="0 0 200 200" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Languages</title>
    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <circle fill="#FFFFFF" opacity="0.1" cx="100" cy="100" r="100"></circle>
        <g transform="translate(50, 50)" stroke="#FFFFFF" stroke-width="2">
            <rect x="0" y="0" width="100" height="100" rx="4"></rect>
            <line x1="0" y1="25" x2="100" y2="25" stroke-linecap="round"></line>
            <text x="10" y="17" font-family="monospace" font-size="12" fill="#FFFFFF">{ }</text>
            <text x="80" y="17" font-family="monospace" font-size="12" fill="#FFFFFF">&lt;/&gt;</text>
            
            <!-- JavaScript -->
            <g transform="translate(10, 40)">
                <text font-family="monospace" font-size="10" fill="#FFFFFF">JS</text>
                <line x1="20" y1="0" x2="70" y2="0" stroke-linecap="round"></line>
            </g>
            
            <!-- Python -->
            <g transform="translate(10, 55)">
                <text font-family="monospace" font-size="10" fill="#FFFFFF">PY</text>
                <line x1="20" y1="0" x2="50" y2="0" stroke-linecap="round"></line>
            </g>
            
            <!-- HTML -->
            <g transform="translate(10, 70)">
                <text font-family="monospace" font-size="10" fill="#FFFFFF">HTML</text>
                <line x1="30" y1="0" x2="80" y2="0" stroke-linecap="round"></line>
            </g>
            
            <!-- CSS -->
            <g transform="translate(10, 85)">
                <text font-family="monospace" font-size="10" fill="#FFFFFF">CSS</text>
                <line x1="30" y1="0" x2="60" y2="0" stroke-linecap="round"></line>
            </g>
        </g>
    </g>
</svg>
