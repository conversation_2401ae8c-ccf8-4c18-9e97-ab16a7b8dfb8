<?xml version="1.0" encoding="UTF-8"?>
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#003092" />
      <stop offset="100%" stop-color="#00879E" />
    </linearGradient>
    <linearGradient id="bg-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#003092" stop-opacity="0.1" />
      <stop offset="100%" stop-color="#FFAB5B" stop-opacity="0.1" />
    </linearGradient>
  </defs>
  <!-- House shape with background fill -->
  <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" stroke="url(#gradient)" fill="url(#bg-gradient)"/>
  <!-- Code brackets -->
  <polyline points="9 14 7 16 9 18" stroke="url(#gradient)" stroke-width="2.5"/>
  <polyline points="15 14 17 16 15 18" stroke="url(#gradient)" stroke-width="2.5"/>
  <!-- Slash in the middle -->
  <line x1="12" y1="13" x2="12" y2="19" stroke="url(#gradient)" stroke-width="2.5"/>
  <line x1="14" y1="13" x2="10" y2="19" stroke="url(#gradient)" stroke-width="2.5"/>
</svg>
