/* This file can be used for global styles that are not covered by Tailwind */

/* Editor styles */
.editor-container {
  height: 60vh;
  border-radius: 0.5rem;
  overflow: hidden;
  border: 1px solid #e5e7eb;
}

.dark .editor-container {
  border-color: #374151;
}

/* Output styles */
.output-container {
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
  background-color: #f9fafb;
  padding: 1rem;
  margin-top: 1rem;
}

.dark .output-container {
  border-color: #374151;
  background-color: #1f2937;
}

.output-content {
  font-family: 'Fira Code', monospace;
  white-space: pre-wrap;
  overflow-x: auto;
  padding: 0.75rem;
  border-radius: 0.25rem;
  background-color: #ffffff;
  color: #1f2937;
}

.dark .output-content {
  background-color: #111827;
  color: #e5e7eb;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.dark ::-webkit-scrollbar-track {
  background: #1f2937;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.dark ::-webkit-scrollbar-thumb {
  background: #4b5563;
}

::-webkit-scrollbar-thumb:hover {
  background: #a0a0a0;
}

.dark ::-webkit-scrollbar-thumb:hover {
  background: #6b7280;
}